:: Build and run docker compose
:: docker compose -f .\docker-compose.jainul.yml up -d --build

:: Import SQL dump
:: docker exec -i fhr-mobileronline-fhr-db-1 sh -c "mysql -u root -pmagento2 magento2 < /var/backups/fhr_PROD.sql"

:: Run SQL script after load production dump
:: docker exec -i fhr-mobileronline-fhr-db-1 sh -c 'mysql -u root -pmagento2 magento2 < /var/sql/after_db_load_dump_from_production.sql'

:: Run composer install
:: docker exec -it fhr-mobileronline-fhr-fpm-1 composer config process-timeout 900
:: docker exec -it fhr-mobileronline-fhr-fpm-1 composer install

:: Set permissions
:: docker exec -it fhr-mobileronline-fhr-fpm-1 bash ./docker_permissions.sh

:: Install tailwind dependencies
:: docker exec -it fhr-mobileronline-fhr-fpm-1 bash -c "cd /var/www/html/app/design/frontend/MadHat/technology/web/tailwind && npm install"

:: Run Magento commands to setup
:: docker exec -it fhr-mobileronline-fhr-fpm-1 php bin/magento se:up
:: docker exec -it fhr-mobileronline-fhr-fpm-1 php bin/magento se:di:co
:: docker exec -it fhr-mobileronline-fhr-fpm-1 php bin/magento ind:res
:: docker exec -it fhr-mobileronline-fhr-fpm-1 php bin/magento ind:rei
:: docker exec -it fhr-mobileronline-fhr-fpm-1 bash -c "npm --prefix app/design/frontend/MadHat/technology/web/tailwind run watch"

