#!/bin/bash
# Get the current full path
current_path=$(pwd)

# Construct the prompt string with the current full path
prompt="Enter your choice (q OR f) at $current_path: "

echo "Select quick or full:"
echo "q. quick - only change ownership chown -R $(whoami):www-data for $current_path"
echo "f. full, use if quick not help, change ownership chown -R $(whoami):www-data and all directories chmod 775 and all files chmod 664"

# Prompt the user
read -p "$prompt" choice

case $choice in
  q)
    echo "chown -R $(whoami):www-data $current_path"
    sudo chown -R $(whoami):www-data $current_path
    ;;
  f)
    echo "chown -R $(whoami):www-data $current_path"
    sudo chown -R $(whoami):www-data $current_path

    echo "Changing directory permissions to 775:"
    sudo find $current_path -type d -exec chmod 775 {} \;

    echo "Changing file permissions to 664:"
    sudo find $current_path -type f -exec chmod 664 {} \;
    ;;
  *)
    echo "Invalid choice. Please enter q OR f"
    ;;
esac

echo "Done."
