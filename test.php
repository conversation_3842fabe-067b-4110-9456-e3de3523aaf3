<?php
/**
 * File for test debug
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
ini_set('memory_limit', '5G');
error_reporting(E_ALL);

use Magento\Framework\App\Bootstrap;
require 'app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);

$objectManager = $bootstrap->getObjectManager();

$state = $objectManager->get('Magento\Framework\App\State');
//$state->setAreaCode(\Magento\Framework\App\Area::AREA_FRONTEND);
$state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);

echo __('<pre>Test</pre><br>');

// @TODO: Set you test code here
//$common = $objectManager->get(\Fhr\OrderCreator\Model\Common::class);
//
//$customerId = 4334;
//$productsItemData = '9488=>1;1066=>1';
//$common->createOrder($customerId, $productsItemData);
