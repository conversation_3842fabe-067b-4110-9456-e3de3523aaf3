#!/bin/bash

DIR=$(cd `dirname $0` && pwd)

php bin/magento i18n:collect-phrases "$DIR"/app/code -o "$DIR"/var/i18n_collect-phrases_code.csv &&
php bin/magento i18n:collect-phrases "$DIR"/app/design -o "$DIR"/var/i18n_collect-phrases_design.csv &&
php bin/magento i18n:collect-phrases "$DIR"/vendor/magecomp -o "$DIR"/var/i18n_collect-phrases_magecomp.csv &&
php bin/magento i18n:collect-phrases "$DIR"/vendor/magefan -o "$DIR"/var/i18n_collect-phrases_magefan.csv &&
php bin/magento i18n:collect-phrases "$DIR"/vendor/mirasvit -o "$DIR"/var/i18n_collect-phrases_mirasvit.csv &&
php bin/magento i18n:collect-phrases "$DIR"/vendor/prince -o "$DIR"/var/i18n_collect-phrases_prince.csv &&

cat "$DIR"/var/i18n_collect-phrases_code.csv "$DIR"/var/i18n_collect-phrases_design.csv "$DIR"/var/i18n_collect-phrases_magecomp.csv "$DIR"/var/i18n_collect-phrases_magefan.csv "$DIR"/var/i18n_collect-phrases_mirasvit.csv "$DIR"/var/i18n_collect-phrases_prince.csv > "$DIR"/var/i18n_collect-phrases.csv &&

php bin/magento fhr_translatehelper:processi18ncollectphrases "$DIR"/var/i18n_collect-phrases.csv sv_SE &&
php bin/magento fhr_translatehelper:processi18ncollectphrases "$DIR"/var/i18n_collect-phrases.csv fr_FR &&

rm "$DIR"/var/i18n_collect-phrases_code.csv "$DIR"/var/i18n_collect-phrases_design.csv "$DIR"/var/i18n_collect-phrases_magecomp.csv "$DIR"/var/i18n_collect-phrases_magefan.csv "$DIR"/var/i18n_collect-phrases_mirasvit.csv "$DIR"/var/i18n_collect-phrases_prince.csv