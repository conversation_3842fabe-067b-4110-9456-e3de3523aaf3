UPDATE core_config_data SET `value` = "http://fhrm24.local/" WHERE `path` = "web/unsecure/base_url" AND scope_id = 0;
UPDATE core_config_data SET `value` = "https://fhrm24.local/" WHERE `path` = "web/secure/base_url" AND scope_id = 0;
UPDATE core_config_data SET `value` = "https://fhrm24.local/" WHERE `path` = "web/unsecure/base_link_url" AND scope_id = 0;
UPDATE store_website SET `code` = "fhr", `name` = "fhrm24.local" WHERE `website_id` = 1;
UPDATE store_website SET `code` = "fhrparts", `name` = "fhrpartsm24.local" WHERE `website_id` = 2;
UPDATE core_config_data SET `value` = "http://fhrm24.local/" WHERE `path` = "web/unsecure/base_url" AND scope = "websites" AND scope_id = 1;
UPDATE core_config_data SET `value` = "https://fhrm24.local/" WHERE `path` = "web/secure/base_url" AND scope = "websites" AND scope_id = 1;
UPDATE core_config_data SET `value` = "http://fhrpartsm24.local/" WHERE `path` = "web/unsecure/base_url" AND scope = "websites" AND scope_id = 2;
UPDATE core_config_data SET `value` = "https://fhrpartsm24.local/" WHERE `path` = "web/secure/base_url" AND scope = "websites" AND scope_id = 2;
UPDATE core_config_data SET `value` = "http://fhrpartsm24.local/" WHERE `path` = "web/unsecure/base_link_url" AND scope = "websites" AND scope_id = 2;
UPDATE core_config_data SET `value` = "https://fhrpartsm24.local/" WHERE `path` = "web/secure/base_link_url" AND scope = "websites" AND scope_id = 2;
DELETE FROM core_config_data WHERE `path` = "web/unsecure/base_media_url";
DELETE FROM core_config_data WHERE `path` = "web/secure/base_media_url";
UPDATE core_config_data SET `value` = "smtp.mailtrap.io" WHERE `path` = "smtp/configuration_option/host";
UPDATE core_config_data SET `value` = "4610a072883db9" WHERE `path` = "smtp/configuration_option/username";
UPDATE core_config_data SET `value` = "bb6bbcea268e00" WHERE `path` = "smtp/configuration_option/password";
UPDATE core_config_data SET `value` = "0" WHERE `path` = "smtp/developer/developer_mode";
UPDATE core_config_data SET `value` = "1" WHERE `path` = "payment/netsnetaxept/test_mode";
UPDATE core_config_data SET `value` = "PK19334_a7ccc659470f" WHERE `path` = "klarna/api/merchant_id";
UPDATE core_config_data SET `value` = "Kgj7QfAYXjsMo5wo" WHERE `path` = "klarna/api/shared_secret";
UPDATE core_config_data SET `value` = "1" WHERE `path` = "klarna/api/test_mode";
UPDATE core_config_data SET `value` = "1" WHERE `path` = "klarna/api/debug";
UPDATE core_config_data SET `value` = "0" WHERE `path` = "msp_securitysuite_recaptcha/frontend/enabled";
UPDATE core_config_data SET `value` = "6eMkNPxfF8Beg4QV" WHERE `path` = "ipiccolo/general/SecretKey";
UPDATE core_config_data SET `value` = "FhrParts200" WHERE `path` = "ipiccolo/general/CompanyId";
UPDATE core_config_data SET `value` = "https://sitest.inkclub.com" WHERE `path` = "ipiccolo/general/url";
UPDATE core_config_data SET `value` = "1" WHERE `path` = "payment/paypal_standard/sandbox_flag";
UPDATE core_config_data SET `value` = "1" WHERE `path` = "paypal/wpp/sandbox_flag";
UPDATE core_config_data SET `value` = "sb-srah91101624_api1.business.example.com" WHERE `path` = "paypal/wpp/api_username";
UPDATE core_config_data SET `value` = "87JDPC5J63RH5VQD" WHERE `path` = "paypal/wpp/api_password";
UPDATE core_config_data SET `value` = "Aw-hoBjK5vCB7NBs-sT.atT7LSJIAa8BH8h0zlC1exPmqsopM.ZJRwc." WHERE `path` = "paypal/wpp/api_signature";
UPDATE core_config_data SET `value` = "elasticsearch.fhr.docker" WHERE `path` = "catalog/search/elasticsearch7_server_hostname";
DELETE FROM core_config_data WHERE `path` LIKE "%cookie%";
DELETE FROM core_config_data WHERE `path` LIKE "%design/head/includes%";
DELETE FROM core_config_data WHERE `path` LIKE "%design/footer/absolute_footer%";
