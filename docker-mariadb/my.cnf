[mysqld]
# Memory and packet settings
max_allowed_packet = 1G
innodb_buffer_pool_size = 4G
innodb_log_file_size = 1G
innodb_log_buffer_size = 64M
innodb_ft_cache_size = 40000000

# Connection and timeout settings
wait_timeout = 28800
interactive_timeout = 28800
net_read_timeout = 1200
net_write_timeout = 1200
connect_timeout = 120

# Performance settings
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_io_capacity = 2000
innodb_io_capacity_max = 4000
innodb_write_io_threads = 8
innodb_read_io_threads = 8

# Import optimization
innodb_doublewrite = 0
innodb_change_buffering = all
innodb_adaptive_hash_index = 0

# Disable strict mode for import
sql_mode = ""

# Network settings
bind-address = 0.0.0.0

# Temp table settings
tmp_table_size = 512M
max_heap_table_size = 512M
