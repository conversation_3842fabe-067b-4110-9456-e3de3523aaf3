<?php
/**
 * <PERSON>ript leave only last $limit orders
 * other orders with invoices, shipments, credit memos, customer and products will be deleted
 */

die('This is dangerius script delete line '.__LINE__.' to run it');
error_reporting(1);
ini_set('max_execution_time', 0);

use \Magento\Framework\App\Bootstrap;

require __DIR__ . '/app/bootstrap.php';
$bootstrap = Bootstrap::create(BP, $_SERVER);
$objectManager = $bootstrap->getObjectManager();
//$objectManager = \Magento\Framework\App\ObjectManager::getInstance();

$limit = 1000;
$customersWhitelist = [];
$productsWhitelist = [];

$orderCollection = $objectManager->get('\Magento\Sales\Model\ResourceModel\Order\CollectionFactory')->create();
$orderCollection->setOrder('entity_id', 'DESC');
echo 'Total orders: ' . $orderCollection->count() . "\n";
$registry = $objectManager->get('\Magento\Framework\Registry');
$i = 1;
if ($registry->registry('isSecureArea')) {
    $registry->unregister('isSecureArea');
}
$registry->register('isSecureArea', true);
$state = $objectManager->get('\Magento\Framework\App\State');
$state->setAreaCode(\Magento\Framework\App\Area::AREA_ADMINHTML);
foreach ($orderCollection as $order) {
    echo 'Order ID: ' . $order->getId() . "\n";
    $customersWhitelist[] = $order->getCustomerId();
    foreach ($order->getAllItems() as $item) {
        $productsWhitelist[] = $item->getProductId();
    }
    if ($i > $limit) {
        // Delete Invoice
        $_invoices = $order->getInvoiceCollection();
        if ($_invoices) {
            foreach ($_invoices as $_invoice) {
                echo 'Delete invoice ID:  ' . $_invoice->getId() . "\n";
                $_invoice->delete();
            }
        }
        // Delete Shipment
        $_shipments = $order->getShipmentsCollection();
        if ($_shipments) {
            foreach ($_shipments as $_shipment) {
                echo 'Delete shipment ID:  ' . $_shipment->getId() . "\n";
                $_shipment->delete();
            }
        }
        // Delete Creditmemo
        $_creditmemos = $order->getCreditmemosCollection();
        if ($_creditmemos) {
            foreach ($_creditmemos as $_creditmemo) {
                echo 'Delete shipment ID:  ' . $_creditmemo->getId() . "\n";
                $_creditmemo->delete();
            }
        }
        echo 'Delete order ID: ' . $order->getId() . "\n";
        $order->delete();
    }
    $i++;
}
$customersWhitelist = array_unique($customersWhitelist);
$productsWhitelist = array_unique($productsWhitelist);

// Delete customers
$customerCollection = $objectManager->get('\Magento\Customer\Model\ResourceModel\Customer\CollectionFactory')->create();
echo str_repeat('-', 63) . "\n";
print_r($customersWhitelist);
echo str_repeat('-', 63) . "\n";
echo 'Total customers: ' . $customerCollection->count() . "\n";
foreach ($customerCollection as $customer) {
    if (!in_array($customer->getId(), $customersWhitelist)) {
        echo 'Delete customer ID:  ' . $customer->getId() . "\n";
        $customer->delete();
    }
}
// Delete products
$productsCollection = $objectManager->get('\Magento\Catalog\Model\ResourceModel\Product\CollectionFactory')->create();
echo str_repeat('-', 63) . "\n";
print_r($productsWhitelist);
echo str_repeat('-', 63) . "\n";
echo 'Total products: ' . $productsCollection->count() . "\n";
foreach ($productsCollection as $product) {
    if (!in_array($product->getId(), $productsWhitelist)) {
        echo 'Delete product ID:  ' . $product->getId() . "\n";
        $product->delete();
    }
}

echo "\n";
echo str_repeat('=', 63);
echo "\n";
$orderCollection = $objectManager->get('\Magento\Sales\Model\ResourceModel\Order\CollectionFactory')->create();
echo 'Total orders: ' . $orderCollection->count() . "\n";
$customerCollection = $objectManager->get('\Magento\Customer\Model\ResourceModel\Customer\CollectionFactory')->create();
echo 'Total customers: ' . $customerCollection->count() . "\n";
$productsCollection = $objectManager->get('\Magento\Catalog\Model\ResourceModel\Product\CollectionFactory')->create();
echo 'Total products: ' . $productsCollection->count() . "\n";
$registry->unregister('isSecureArea');