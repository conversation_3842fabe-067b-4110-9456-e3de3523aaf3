#!/bin/bash

# Docker containers and MySQL credentials
DB_CONTAINER="mobileronlinelocal-fhr-db-1"
FPM_CONTAINER="mobileronlinelocal-fhr-fpm-1"
MYSQL_USER="magento2"
MYSQL_PASSWORD="magento2"
MYSQL_DATABASE="magento2"
MYSQL_ROOT_USER="root"
RESTORE_FILE="./docker-mariadb/backups/fhr_PROD.sql"

# Function to check if a command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Check for required commands
REQUIRED_COMMANDS=("pv" "rsync" "lbunzip2" "docker")
MISSING_COMMANDS=()

for cmd in "${REQUIRED_COMMANDS[@]}"; do
  if ! command_exists "$cmd"; then
    MISSING_COMMANDS+=("$cmd")
  fi
done

if [ ${#MISSING_COMMANDS[@]} -ne 0 ]; then
  echo "The following required commands are missing:"
  for cmd in "${REQUIRED_COMMANDS[@]}"; do
    echo "  - $cmd"
  done
  echo "Please install them and try again."
  exit 1
fi

if [ ! -f "$RESTORE_FILE" ]; then
  echo 'Rsync - get last backup from production'
  echo
  rsync --progress -avHe ssh magento2@*************:/home/<USER>/www/fhrparts.com/var/backups/fhr_PROD_last.sql.bz2 ./docker-mariadb/backups/fhr_PROD.sql.bz2
  pv ./docker-mariadb/backups/fhr_PROD.sql.bz2 | lbunzip2 > "$RESTORE_FILE"
  echo 'Updating Definer in SQL Dump...'
  sed -i 's/DEFINER=`fhrse`@`localhost`/DEFINER=`magento2`@`localhost`/g' "$RESTORE_FILE"
fi

echo 'Restore DB from backup'
echo
pv "$RESTORE_FILE" | docker exec -i "$DB_CONTAINER" mysql -u"$MYSQL_ROOT_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE"

# Execute SQL commands via docker exec
docker exec "$DB_CONTAINER" mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" -e 'UPDATE admin_user SET password = CONCAT(SHA2("xxxxxxxx1366613nox", 256), ":xxxxxxxx:1") WHERE 1;'

# Run Magento commands in the FPM container
docker exec "$FPM_CONTAINER" php bin/magento se:up
docker exec "$FPM_CONTAINER" php bin/magento se:di:co

# Configuration updates
# Import additional SQL file
docker exec -i "$DB_CONTAINER" mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" "$MYSQL_DATABASE" < ./docker-mariadb/sql/after_db_load_dump_from_production.sql

# Cleanup
rm ./docker-mariadb/backups/fhr_PROD.sql.bz2
read -r -p "Delete fhr_PROD.sql? If you delete it backup will be downloaded on next script run again. [Y/n]" input
case $input in
  [yY])
    rm "$RESTORE_FILE"
    echo "File fhr_PROD.sql deleted"
    echo
    ;;
  *)
    echo "File fhr_PROD.sql not deleted"
    echo
    ;;
esac
