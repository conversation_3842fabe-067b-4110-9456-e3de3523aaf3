FROM nginx:1.18

# Copy Nginx configuration
COPY default.conf /etc/nginx/conf.d/default.conf

# Copy Magento source code to Nginx html directory
COPY . /var/www/html/

# Copy SSL certificate generation script
COPY ssl/generate_ssl.sh /tmp/generate_ssl.sh

# Execute SSL certificate generation during the build
RUN chmod +x /tmp/generate_ssl.sh \
    && /bin/bash /tmp/generate_ssl.sh \
    && rm /tmp/generate_ssl.sh  # Optionally remove the script after execution

# Expose port 80 and 443 for HTTP and HTTPS
EXPOSE 80 443
