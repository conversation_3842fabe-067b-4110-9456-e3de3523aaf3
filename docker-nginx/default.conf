# File: nginx/default.conf
map $http_host $MAGE_RUN_CODE {
    default '';
    fhrm24.local fhr;
    fhrpartsm24.local fhrparts;
}

upstream fastcgi_backend {
    server fpm.fhr.docker:9000; # Specify the correct hostname and port
}

#server {
#    listen 80;
#    server_name magento.local;
#    set $MAGE_ROOT /var/www/html;
#    include /var/www/html/nginx.conf.sample;
#}

server {
    listen 80;
    server_name fhrm24.local;

    set $MAGE_ROOT /var/www/html;
    set $MAGE_MODE developer;
    set $MAGE_RUN_TYPE website;
    set $MAGE_RUN_CODE fhr;

    include /var/www/html/nginx.conf.sample;
}

server {
    listen 80;
    server_name fhrpartsm24.local;

    set $MAGE_ROOT /var/www/html;
    set $MAGE_MODE developer;
    set $MAGE_RUN_TYPE website;
    set $MAGE_RUN_CODE fhrparts;

    include /var/www/html/nginx.conf.sample;
}

server {
    listen 443 ssl;
    server_name fhrm24.local;

    ssl_certificate /etc/nginx/ssl/nginx.crt;
    ssl_certificate_key /etc/nginx/ssl/nginx.key;

    set $MAGE_ROOT /var/www/html;
    set $MAGE_MODE developer;
    set $MAGE_RUN_TYPE website;
    set $MAGE_RUN_CODE fhr;

    include /var/www/html/nginx.conf.sample;
}

server {
    listen 443 ssl;
    server_name fhrpartsm24.local;

    ssl_certificate /etc/nginx/ssl/nginx.crt;
    ssl_certificate_key /etc/nginx/ssl/nginx.key;

    set $MAGE_ROOT /var/www/html;
    set $MAGE_MODE developer;
    set $MAGE_RUN_TYPE website;
    set $MAGE_RUN_CODE fhrparts;

    include /var/www/html/nginx.conf.sample;
}
