{"name": "magento/project-community-edition", "description": "eCommerce Platform for Growth (Community Edition)", "type": "project", "license": ["OSL-3.0", "AFL-3.0"], "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/*": true, "cweagans/composer-patches": true}, "preferred-install": "dist", "sort-packages": true, "process-timeout": 900}, "version": "2.4.5-p1", "require": {"aitoc/google-customer-reviews": "^1.0", "amasty/module-free-gift-subscription-package-lite": "^1.4", "aune-io/magento2-product-grid-category-filter": "^1.3", "belvg/module-color-order-status": "^2.0", "bsscommerce/disable-compare": "^1.0", "cweagans/composer-patches": "^1.6", "cynoinfotech/paymentrestrictions": "^1.0", "ekouk/imagecleaner": "dev-master", "elgentos/regenerate-catalog-urls": "^0.2.5", "faonni/module-smart-category-kit": "^2.3", "fooman/emailattachments-m2": "^3.0", "fooman/printorderpdf-m2": "^3.0", "fruitcake/magento2-alwaysloginascustomer": "^1.2", "ghoster/changecustomerpassword": "^1.0", "hyva-themes/hyva-ui": "^2.4", "hyva-themes/magento2-cms-tailwind-jit": "^1.1", "hyva-themes/magento2-default-theme": "^1.3", "hyva-themes/magento2-luma-checkout": "^1.1", "hyva-themes/magento2-magezon-ninja-menus": "^1.0", "jajuma/hyva-faq": "^1.1", "laminas/laminas-serializer": "^2.14", "laminas/laminas-zendframework-bridge": "^1.8", "magecomp/magento-2-order-comments": "^1.0", "magento/composer-dependency-version-audit-plugin": "~0.1", "magento/composer-root-update-plugin": "~2.0", "magento/data-migration-tool": "^2.4", "magento/product-community-edition": "2.4.6-p11", "mageplaza/magento-2-arabic-language-pack": "dev-master", "mageplaza/magento-2-danish-language-pack": "dev-master", "mageplaza/magento-2-finnish-language-pack": "dev-master", "mageplaza/magento-2-french-language-pack": "dev-master", "mageplaza/magento-2-german-language-pack": "dev-master", "mageplaza/magento-2-greek-language-pack": "dev-master", "mageplaza/magento-2-italian-language-pack": "dev-master", "mageplaza/magento-2-norwegian-language-pack": "dev-master", "mageplaza/magento-2-spanish-language-pack": "dev-master", "mageplaza/magento-2-swedish-language-pack": "dev-master", "mageplaza/module-banner-slider": "^4.0", "mageplaza/module-smtp": "^1.3", "mageworx/module-ordereditormeta": "^1.9.12", "meetanshi/magento2-shipping-restrictions": "^1.0", "meetanshi/module-flatshipping": "^1.0", "mirasvit/module-credit": "*", "mirasvit/module-rma": "^2.2", "mirasvit/module-rma-hyva": "*", "olegkoval/magento2-regenerate-url-rewrites": "^1.4", "phoenix/module-bankpayment": "^1.1", "phpoffice/phpspreadsheet": "^1.12", "prince/magento2-extrafeepro": "1.1.0", "prince/module-faq": "^2.0"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\": "app/code/Magento/", "Magento\\Framework\\": "lib/internal/Magento/Framework/", "Magento\\Setup\\": "setup/src/Magento/Setup/"}}, "require-dev": {"allure-framework/allure-phpunit": "^2", "dealerdirect/phpcodesniffer-composer-installer": "^1.0", "dg/bypass-finals": "^1.4", "friendsofphp/php-cs-fixer": "^3.8", "lusitanian/oauth": "^0.8", "magento/magento2-functional-testing-framework": "^4.3.1", "pdepend/pdepend": "^2.10", "phpmd/phpmd": "^2.12.0", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "sebastian/comparator": "<=4.0.6", "symfony/finder": "^5.4"}, "conflict": {"gene/bluefoot": "*"}, "autoload-dev": {"psr-4": {"Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/", "Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/"}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"private-packagist": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/fhrparts-com/"}, "amasty": {"type": "composer", "url": "https://composer.amasty.com/community/"}, "0": {"type": "composer", "url": "https://repo.magento.com/"}, "1": {"type": "artifact", "url": "extensions/"}, "mirasvit-scr2": {"type": "composer", "url": "https://45777:<EMAIL>/45777:WMJEHD369Z/"}, "mirasvit-rma2": {"type": "composer", "url": "https://45252:<EMAIL>/45252:KIHO8PA759/"}, "mirasvit-ssu2": {"type": "composer", "url": "https://48218:<EMAIL>/48218:GHO72436AS/"}, "ekouk-imagecleaner": {"type": "vcs", "url": "https://github.com/cannycookie/Mage2UnusedImageRemover"}}, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "patches": {"magento/framework": {"FIX Warning: Trying to access array offset on value of type null in /var/www/html/vendor/magento/framework/Mview/View/Subscription.php on line 344": "patches/composer/2_4_6-p9/88688710293bdec3de9bdc19df2419ce1ee7c7b4.patch"}}}}