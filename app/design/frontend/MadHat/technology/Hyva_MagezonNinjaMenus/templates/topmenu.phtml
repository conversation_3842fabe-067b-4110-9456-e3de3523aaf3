<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

/**
 * @var TopMenu $block
 */
$html = $block->getMenuHtml();

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;
use Magezon\NinjaMenus\Block\TopMenu;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Navigation $viewModelNavigation */
/** @var HeroiconsOutline $heroicons */

$heroicons = $viewModels->require(HeroiconsOutline::class);
$viewModelNavigation = $viewModels->require(Navigation::class, $block);
$uniqueId = '_' . uniqid();
$maxLevel = 4;
// Increase depth to match desktop menu (6 levels total)
$menuItems = $viewModelNavigation->getNavigation(2 + $maxLevel);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

// Simple function to render mobile menu items with proper navigation
if (!function_exists('renderMobileMenuItems')) {
    function renderMobileMenuItems($items, $heroicons, $escaper, $level = 0, $parentIndex = '') {
        if (empty($items)) return '';

        $html = '';
        foreach ($items as $index => $menuItem) {
            $currentIndex = $parentIndex ? $parentIndex . '-' . $index : $index;
            $hasChildren = !empty($menuItem['childData']);

            // Calculate styling based on level
            $indentation = $level * 4;
            $textSize = $level === 0 ? 'text-base font-medium' : 'text-sm';
            $textColor = $level === 0 ? 'text-gray-900' : 'text-gray-700';

            $html .= '<li class="level-' . $level . '">';

            // Menu item container
            $html .= '<div class="flex items-center">';

            // Menu item link
            $html .= '<a class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container"';
            $html .= ' href="' . $escaper->escapeUrl($menuItem['url']) . '"';
            $html .= ' title="' . $escaper->escapeHtmlAttr($menuItem['name']) . '">';
            $html .= '<span class="ml-' . $indentation . ' ' . $textSize . ' ' . $textColor . '">' . $escaper->escapeHtml($menuItem['name']) . '</span>';
            $html .= '</a>';

            // Arrow button for subcategories
            if ($hasChildren) {
                $html .= '<button @click="navigateToLevel(\'' . $currentIndex . '\')"';
                $html .= ' class="absolute right-0 flex items-center justify-center w-11 h-11 mr-8 cursor-pointer bg-container-lighter border-container"';
                $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Open %1 subcategories', $menuItem['name'])) . '">';
                $html .= '<div class="w-8 h-8 border rounded">';
                $html .= $heroicons->chevronRightHtml('w-full h-full p-1', 20, 20, ["aria-hidden" => "true"]);
                $html .= '</div></button>';
            }

            $html .= '</div>';
            $html .= '</li>';
        }

        return $html;
    }

    // Function to render navigation panels for all levels
    function renderNavigationPanels($items, $heroicons, $escaper, $level = 0, $parentIndex = '', $parentName = '') {
        if (empty($items)) return '';

        $html = '';
        foreach ($items as $index => $menuItem) {
            $currentIndex = $parentIndex ? $parentIndex . '-' . $index : $index;
            $hasChildren = !empty($menuItem['childData']);

            if ($hasChildren) {
                // Create panel for this menu item
                $html .= '<div data-panel-id="' . $escaper->escapeHtmlAttr($currentIndex) . '"';
                $html .= ' class="absolute top-0 right-0 z-20 flex flex-col gap-1 w-full h-full p-1 bg-container-lighter hidden"';
                $html .= ' :class="{ \'hidden\': currentPanel !== \'' . $currentIndex . '\', \'flex\': currentPanel === \'' . $currentIndex . '\' }">';

                $html .= '<ul class="mt-16 transition-transform duration-200 ease-in-out translate-x-full transform"';
                $html .= ' :class="{ \'translate-x-full\' : currentPanel !== \'' . $currentIndex . '\', \'translate-x-0\' : currentPanel === \'' . $currentIndex . '\' }">';

                // Back button
                if ($level === 0) {
                    $html .= '<li><button type="button"';
                    $html .= ' class="flex items-center px-8 py-4 border-b cursor-pointer bg-container border-container w-full border-t"';
                    $html .= ' @click="goBack()"';
                    $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Back to main menu')) . '">';
                    $html .= $heroicons->chevronLeftHtml('', 24, 24, ["aria-hidden" => "true"]);
                    $html .= '<span class="ml-4">' . $escaper->escapeHtml(__('Main Menu')) . '</span>';
                    $html .= '</button></li>';
                } else {
                    $html .= '<li><button type="button"';
                    $html .= ' class="flex items-center px-8 py-4 border-b cursor-pointer bg-container border-container w-full border-t"';
                    $html .= ' @click="goBack()"';
                    $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Back to %1', $parentName)) . '">';
                    $html .= $heroicons->chevronLeftHtml('', 24, 24, ["aria-hidden" => "true"]);
                    $html .= '<span class="ml-4">' . $escaper->escapeHtml($parentName) . '</span>';
                    $html .= '</button></li>';
                }

                // View All link
                $html .= '<li><a href="' . $escaper->escapeUrl($menuItem['url']) . '"';
                $html .= ' title="' . $escaper->escapeHtmlAttr($menuItem['name']) . '"';
                $html .= ' class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container">';
                $html .= '<span class="ml-10 font-medium">' . $escaper->escapeHtml(__('View All %1', $menuItem['name'])) . '</span>';
                $html .= '</a></li>';

                // Render child items
                $html .= renderMobileMenuItems($menuItem['childData'], $heroicons, $escaper, $level + 1, $currentIndex);

                $html .= '</ul></div>';

                // Recursively render panels for children
                $html .= renderNavigationPanels($menuItem['childData'], $heroicons, $escaper, $level + 1, $currentIndex, $menuItem['name']);
            }
        }

        return $html;
    }
}

// Get custom menu items from variables (same as desktop template)
$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
/** @var VariableFactory $variableFactory */
$variableModel = $objectManager->create(\Magento\Variable\Model\Variable::class);

$fhrUrlSparesParts = $variableModel->loadByCode('fhr_url_spare_parts')->getPlainValue();
$fhrUrlAccessories = $variableModel->loadByCode('fhr_url_accessories')->getPlainValue();
$fhrUrlUsedPhones = $variableModel->loadByCode('fhr_url_used_phones')->getPlainValue();
$fhrUrlBargainCorner = $variableModel->loadByCode('fhr_url_bargain_corner')->getPlainValue();

// Debug: Log menu structure to see the actual data
error_log('=== MOBILE MENU DEBUG ===');
error_log('Menu items count: ' . count($menuItems));
foreach ($menuItems as $index => $item) {
    error_log("Level 0 [$index]: " . $item['name'] . " (children: " . (isset($item['childData']) ? count($item['childData']) : 0) . ")");
    if (!empty($item['childData'])) {
        foreach ($item['childData'] as $subIndex => $subItem) {
            error_log("  Level 1 [$index-$subIndex]: " . $subItem['name'] . " (children: " . (isset($subItem['childData']) ? count($subItem['childData']) : 0) . ")");
            if (!empty($subItem['childData'])) {
                foreach ($subItem['childData'] as $subSubIndex => $subSubItem) {
                    error_log("    Level 2 [$index-$subIndex-$subSubIndex]: " . $subSubItem['name'] . " (children: " . (isset($subSubItem['childData']) ? count($subSubItem['childData']) : 0) . ")");
                }
            }
        }
    }
}
error_log('=== END MOBILE MENU DEBUG ===');
?>
<div class="navigation hidden lg:flex">
    <?php if($html) :?>
        <div class="sections nav-sections">
            <nav class="navigation" data-action="navigation">
                <?= $html ?>
            </nav>
        </div>
    <?php else: ?>
        <?= $this->getMagentoTopMenuHtml() ?>
    <?php endif; ?>
</div>
<nav
    x-data="initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?>()"
    @load.window="setActiveMenu($root)"
    @keydown.window.escape="closeMenu()"
    class="z-20 navigation lg:hidden size-12"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation')) ?>"
    role="navigation"
>
    <!-- mobile -->
    <button
        x-ref="mobileMenuTrigger"
        @click="openMenu()"
        :class="{'-ml-2 overflow-x-hidden overflow-y-auto fixed top-0 -left-2 w-full' : open}"
        type="button"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Open menu')) ?>"
        aria-haspopup="menu"
        :aria-expanded="open"
        :hidden="open"
    >
        <?= $heroicons->menuHtml('p-2', 48, 48, [":class" => "{ 'hidden' : open, 'block': !open }", "aria-hidden" => "true"]) ?>
    </button>
    <div
        x-ref="mobileMenuNavLinks"
        class="
            fixed top-0 right-0 w-full h-full flex
            flex-col border-t border-container bg-container-lighter
            overflow-y-auto overflow-x-hidden
        "
        :class="{ 'hidden' : !open }"
        :aria-hidden="open ? 'false' : 'true'"
        role="dialog"
        aria-modal="true"
    >
        <?php if($html): ?>
            <!-- NinjaMenus mobile menu with categories -->
            <div class="sections nav-sections border-t flex flex-col gap-y-1 mt-16">
                <nav class="navigation" data-action="navigation">
                    <?= $html ?>
                </nav>
            </div>
        <?php else: ?>
            <!-- Fallback: Standard Hyva mobile menu with categories (enhanced with multi-level support) -->
            <div class="relative w-full h-full">
                <!-- Main menu level -->
                <ul
                    class="border-t flex flex-col gap-y-1 mt-16"
                    aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation links')) ?>"
                    :class="{ 'hidden': currentPanel !== null, 'flex': currentPanel === null }"
                >
                    <?= /* @noEscape */ renderMobileMenuItems($menuItems, $heroicons, $escaper, 0, '') ?>
                </ul>

                <!-- All navigation panels -->
                <?= /* @noEscape */ renderNavigationPanels($menuItems, $heroicons, $escaper, 0, '', '') ?>
            </div>
        <?php endif; ?>

        <!-- Custom menu items for mobile (always show at the end) -->
        <div class="custom-menu-items flex flex-col gap-y-1 mt-4 px-4">
            <a href="<?= $escaper->escapeUrl($fhrUrlSparesParts) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Spares parts')) ?>
            </a>
            <a href="<?= $escaper->escapeUrl($fhrUrlAccessories) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Accessories')) ?>
            </a>
            <a href="<?= $escaper->escapeUrl($fhrUrlUsedPhones) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Used Phones')) ?>
            </a>
            <a href="<?= $escaper->escapeUrl($fhrUrlBargainCorner) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Bargain corner')) ?>
            </a>
        </div>

        <button
            @click="closeMenu()"
            class="fixed top-0 right-0 flex justify-end w-16 self-end mb-1 w-full border-b z-[100] bg-cgrey-0"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Close menu')) ?>"
            type="button"
        >
            <?= $heroicons->xHtml('hidden p-4', 64, 64, [":class" => "{ 'hidden' : !open, 'block': open }", "aria-hidden" => "true"]) ?>
        </button>
    </div>
</nav>
<script>
    'use strict';

    const initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            open: false,
            currentPanel: null,
            navigationStack: [], // Stack to track navigation history

            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('underline');
                });
            },

            openMenu() {
                this.open = true;
                this.currentPanel = null;
                this.navigationStack = [];
                this.$nextTick(() => hyva.trapFocus(this.$refs['mobileMenuNavLinks']));
                document.body.style.position = 'fixed';
            },

            closeMenu() {
                document.body.style.position = '';
                if (this.open) {
                    this.$nextTick(() => this.$refs['mobileMenuTrigger'].focus() || hyva.releaseFocus());
                }
                this.open = false;
                this.currentPanel = null;
                this.navigationStack = [];
            },

            navigateToLevel(panelId) {
                console.log('Navigating to level:', panelId);
                console.log('Current panel:', this.currentPanel);
                console.log('Navigation stack before:', this.navigationStack);

                // Add current panel to stack if we're not at main menu
                if (this.currentPanel !== null) {
                    this.navigationStack.push(this.currentPanel);
                }

                // Set new current panel
                this.currentPanel = panelId;

                console.log('New current panel:', this.currentPanel);
                console.log('Navigation stack after:', this.navigationStack);

                this.$nextTick(() => {
                    const panelElement = document.querySelector('[data-panel-id="' + panelId + '"]');
                    if (panelElement) {
                        hyva.trapFocus(panelElement);
                    }
                });
            },

            goBack() {
                console.log('Going back');
                console.log('Navigation stack before:', this.navigationStack);

                if (this.navigationStack.length > 0) {
                    // Go back to previous panel
                    this.currentPanel = this.navigationStack.pop();
                } else {
                    // Go back to main menu
                    this.currentPanel = null;
                }

                console.log('New current panel:', this.currentPanel);
                console.log('Navigation stack after:', this.navigationStack);

                this.$nextTick(() => {
                    if (this.currentPanel === null) {
                        hyva.trapFocus(this.$refs['mobileMenuNavLinks']);
                    } else {
                        const panelElement = document.querySelector('[data-panel-id="' + this.currentPanel + '"]');
                        if (panelElement) {
                            hyva.trapFocus(panelElement);
                        }
                    }
                });
            }
        }
    }
</script>

<script>
    loadScript('<?= $block->getViewFileUrl('Hyva_MagezonBuilder::js/jquery.min.js') ?>', 'jquery')
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Magezon_NinjaMenus::js/jquery.drilldown.min.js') ?>', 'drilldown');
        })
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Magezon_NinjaMenus::js/jquery.hoverIntent.min.js') ?>', 'hoverIntent');
        })
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Magezon_Core::js/jquery-scrolltofixed-min.js') ?>', 'scrolltofixed');
        })
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Hyva_MagezonNinjaMenus::js/menu.js') ?>', 'menu');
        })
        .then(() => {
            const menuElement = $(".ninjamenus");
            const options = {
                "mobileBreakpoint": 1024,
                "stick": true,
            };
            let ninjaMenus = new NinjaMenus(menuElement, options);
        })
</script>
