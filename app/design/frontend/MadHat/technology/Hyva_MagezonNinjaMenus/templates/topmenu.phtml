<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

/**
 * @var TopMenu $block
 */
$html = $block->getMenuHtml();

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;
use Magezon\NinjaMenus\Block\TopMenu;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Navigation $viewModelNavigation */
/** @var HeroiconsOutline $heroicons */

$heroicons = $viewModels->require(HeroiconsOutline::class);
$viewModelNavigation = $viewModels->require(Navigation::class, $block);
$uniqueId = '_' . uniqid();
$menuItems = $viewModelNavigation->getNavigation(4);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

// Get custom menu items from variables (same as desktop template)
$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
/** @var VariableFactory $variableFactory */
$variableModel = $objectManager->create(\Magento\Variable\Model\Variable::class);

$fhrUrlSparesParts = $variableModel->loadByCode('fhr_url_spare_parts')->getPlainValue();
$fhrUrlAccessories = $variableModel->loadByCode('fhr_url_accessories')->getPlainValue();
$fhrUrlUsedPhones = $variableModel->loadByCode('fhr_url_used_phones')->getPlainValue();
$fhrUrlBargainCorner = $variableModel->loadByCode('fhr_url_bargain_corner')->getPlainValue();
?>
<div class="navigation hidden lg:flex">
    <?php if($html) :?>
        <div class="sections nav-sections">
            <nav class="navigation" data-action="navigation">
                <?= $html ?>
            </nav>
        </div>
    <?php else: ?>
        <?= $this->getMagentoTopMenuHtml() ?>
    <?php endif; ?>
</div>
<nav
    x-data="initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?>()"
    @load.window="setActiveMenu($root)"
    @keydown.window.escape="closeMenu()"
    class="z-20 navigation lg:hidden size-12"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation')) ?>"
    role="navigation"
>
    <!-- mobile -->
    <button
        x-ref="mobileMenuTrigger"
        @click="openMenu()"
        :class="{'-ml-2 overflow-x-hidden overflow-y-auto fixed top-0 -left-2 w-full' : open}"
        type="button"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Open menu')) ?>"
        aria-haspopup="menu"
        :aria-expanded="open"
        :hidden="open"
    >
        <?= $heroicons->menuHtml('p-2', 48, 48, [":class" => "{ 'hidden' : open, 'block': !open }", "aria-hidden" => "true"]) ?>
    </button>
    <div
        x-ref="mobileMenuNavLinks"
        class="
            fixed top-0 right-0 w-full h-full flex
            flex-col border-t border-container bg-container-lighter
            overflow-y-auto overflow-x-hidden
        "
        :class="{ 'hidden' : !open }"
        :aria-hidden="open ? 'false' : 'true'"
        role="dialog"
        aria-modal="true"
    >
        <?php if($html): ?>
            <!-- NinjaMenus mobile menu with categories -->
            <div class="sections nav-sections border-t flex flex-col gap-y-1 mt-16">
                <nav class="navigation" data-action="navigation">
                    <?= $html ?>
                </nav>
            </div>
        <?php else: ?>
            <!-- Fallback: Standard Hyva mobile menu with categories -->
            <ul
                class="border-t flex flex-col gap-y-1 mt-16"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation links')) ?>"
            >
                <?php foreach ($menuItems as $index => $menuItem): ?>
                    <li
                        data-child-id="<?= $escaper->escapeHtmlAttr($index) ?>-main"
                        class="level-0"
                    >
                        <div
                            class="flex items-center transition-transform duration-150 ease-in-out transform"
                            :class="{
                                '-translate-x-full' : mobilePanelActiveId,
                                'translate-x-0' : !mobilePanelActiveId
                            }"
                        >
                            <a
                                class="flex items-center w-full px-8 py-4 border-b cursor-pointer
                                    bg-container-lighter border-container level-0
                                "
                                href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                            >
                                <?= $escaper->escapeHtml($menuItem['name']) ?>
                            </a>
                            <?php if (!empty($menuItem['childData'])): ?>
                                <button
                                    @click="openSubcategory('<?= /* @noEscape */ $index ?>')"
                                    class="absolute right-0 flex items-center justify-center w-11 h-11 mr-8 cursor-pointer
                                    bg-container-lighter border-container"
                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Open %1 subcategories', $menuItem['name'])) ?>"
                                    aria-haspopup="true"
                                    :aria-expanded="mobilePanelActiveId === '<?= /* @noEscape */ (string) $index ?>'"
                                >
                                    <div class="w-8 h-8 border rounded">
                                        <?= $heroicons->chevronRightHtml('w-full h-full p-1', 24, 24, ["aria-hidden" => "true"]) ?>
                                    </div>
                                </button>
                            <?php endif; ?>
                        </div>
                        <?php if (!empty($menuItem['childData'])): ?>
                            <div
                                data-child-id="<?= $escaper->escapeHtmlAttr($index) ?>"
                                class="absolute top-0 right-0 z-10 flex flex-col gap-1 w-full h-full p-1 bg-container-lighter"
                                :class="{
                                    'hidden': mobilePanelActiveId !== '<?= /* @noEscape */ (string) $index ?>'
                                }"
                            >
                                <ul
                                    class="mt-16 transition-transform duration-200 ease-in-out translate-x-full transform"
                                    :class="{
                                        'translate-x-full' : mobilePanelActiveId !== '<?= /* @noEscape */ (string) $index ?>',
                                        'translate-x-0' : mobilePanelActiveId === '<?= /* @noEscape */ (string) $index ?>',
                                    }"
                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Subcategories')) ?>"
                                >
                                    <li>
                                        <button
                                            type="button"
                                            class="flex items-center px-8 py-4 border-b cursor-pointer bg-container border-container w-full border-t"
                                            @click="backToMainCategories('<?= /* @noEscape */ $index ?>-main')"
                                            aria-label="<?= $escaper->escapeHtmlAttr(__('Back to main categories')) ?>"
                                        >
                                            <?= $heroicons->chevronLeftHtml('', 24, 24, ["aria-hidden" => "true"]); ?>
                                            <span class="ml-4">
                                                <?= $escaper->escapeHtml($menuItem['name']) ?>
                                            </span>
                                        </button>
                                    </li>
                                    <li>
                                        <a
                                            href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                            title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                                            class="flex items-center w-full px-8 py-4 border-b cursor-pointer
                                                bg-container-lighter border-container
                                            "
                                        >
                                            <span class="ml-10">
                                                <?= $escaper->escapeHtml(__('View All')) ?>
                                            </span>
                                        </a>
                                    </li>
                                    <?php foreach ($menuItem['childData'] as $subMenuItem): ?>
                                        <li>
                                            <a
                                                href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                                title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                                class="flex items-center w-full px-8 py-4 border-b cursor-pointer
                                                    bg-container-lighter border-container
                                                "
                                            >
                                                <span class="ml-10 text-base text-gray-700">
                                                    <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                                </span>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>

        <!-- Custom menu items for mobile (always show at the end) -->
        <div class="custom-menu-items flex flex-col gap-y-1 mt-4 px-4">
            <a href="<?= $escaper->escapeUrl($fhrUrlSparesParts) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Spares parts')) ?>
            </a>
            <a href="<?= $escaper->escapeUrl($fhrUrlAccessories) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Accessories')) ?>
            </a>
            <a href="<?= $escaper->escapeUrl($fhrUrlUsedPhones) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Used Phones')) ?>
            </a>
            <a href="<?= $escaper->escapeUrl($fhrUrlBargainCorner) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Bargain corner')) ?>
            </a>
        </div>

        <button
            @click="closeMenu()"
            class="fixed top-0 right-0 flex justify-end w-16 self-end mb-1 w-full border-b z-[100] bg-cgrey-0"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Close menu')) ?>"
            type="button"
        >
            <?= $heroicons->xHtml('hidden p-4', 64, 64, [":class" => "{ 'hidden' : !open, 'block': open }", "aria-hidden" => "true"]) ?>
        </button>
    </div>
</nav>
<script>
    'use strict';

    const initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            mobilePanelActiveId: null,
            open: false,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('underline');
                    item.closest('li.level-0') &&
                    item.closest('li.level-0').querySelector('a.level-0').classList.add('underline');
                });
            },
            openMenu() {
                this.open = true
                this.$nextTick(() => hyva.trapFocus(this.$refs['mobileMenuNavLinks']));
                // Prevent from body scrolling while mobile menu opened
                document.body.style.position = 'fixed';
            },
            closeMenu() {
                document.body.style.position = '';

                if (this.open) {
                    this.$nextTick(() => this.$refs['mobileMenuTrigger'].focus() || hyva.releaseFocus());
                }

                this.open = false
                this.mobilePanelActiveId = null
            },
            openSubcategory(index) {
                const menuNodeRef = document.querySelector('[data-child-id=' + index + ']')
                this.mobilePanelActiveId = this.mobilePanelActiveId === index ? 0 : index
                this.$nextTick(() => hyva.trapFocus(menuNodeRef))
            },
            backToMainCategories(index) {
                const menuNodeRef = document.querySelector('[data-child-id=' + index + ']')
                this.mobilePanelActiveId = 0
                this.$nextTick(() => {
                    hyva.trapFocus(this.$refs['mobileMenuNavLinks'])
                    menuNodeRef.querySelector('a').focus()
                })
            }
        }
    }
</script>

<script>
    loadScript('<?= $block->getViewFileUrl('Hyva_MagezonBuilder::js/jquery.min.js') ?>', 'jquery')
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Magezon_NinjaMenus::js/jquery.drilldown.min.js') ?>', 'drilldown');
        })
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Magezon_NinjaMenus::js/jquery.hoverIntent.min.js') ?>', 'hoverIntent');
        })
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Magezon_Core::js/jquery-scrolltofixed-min.js') ?>', 'scrolltofixed');
        })
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Hyva_MagezonNinjaMenus::js/menu.js') ?>', 'menu');
        })
        .then(() => {
            const menuElement = $(".ninjamenus");
            const options = {
                "mobileBreakpoint": 1024,
                "stick": true,
            };
            let ninjaMenus = new NinjaMenus(menuElement, options);
        })
</script>
