<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

/**
 * @var TopMenu $block
 */
$html = $block->getMenuHtml();

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;
use Magezon\NinjaMenus\Block\TopMenu;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Navigation $viewModelNavigation */
/** @var HeroiconsOutline $heroicons */

$heroicons = $viewModels->require(HeroiconsOutline::class);
$viewModelNavigation = $viewModels->require(Navigation::class, $block);
$uniqueId = '_' . uniqid();
$maxLevel = 4;
// Increase depth to match desktop menu (6 levels total)
$menuItems = $viewModelNavigation->getNavigation(2 + $maxLevel);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

// Recursive function to render mobile menu items with infinite levels
if (!function_exists('renderMobileMenuItems')) {
    function renderMobileMenuItems($items, $heroicons, $escaper, $level = 0, $parentIndex = '') {
        if (empty($items)) return '';

        $html = '';
        foreach ($items as $index => $menuItem) {
            $currentIndex = $parentIndex ? $parentIndex . '-' . $index : $index;
            $hasChildren = !empty($menuItem['childData']);

            // Calculate indentation and arrow size based on level
            $indentation = max(0, $level * 4);
            $arrowSize = max(16, 24 - ($level * 2));
            $textSize = $level === 0 ? 'text-base' : ($level === 1 ? 'text-base' : 'text-sm');
            $textColor = $level === 0 ? 'text-gray-900' : ($level === 1 ? 'text-gray-700' : 'text-gray-600');
            $zIndex = 10 + ($level * 10);

            $html .= '<li data-child-id="' . $escaper->escapeHtmlAttr($currentIndex) . '-main" class="level-' . $level . '">';

            // Main menu item container
            $html .= '<div class="flex items-center transition-transform duration-150 ease-in-out transform">';

            // Menu item link
            $html .= '<a class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container level-' . $level . '"';
            $html .= ' href="' . $escaper->escapeUrl($menuItem['url']) . '"';
            $html .= ' title="' . $escaper->escapeHtmlAttr($menuItem['name']) . '">';
            $html .= '<span class="ml-' . $indentation . ' ' . $textSize . ' ' . $textColor . '">' . $escaper->escapeHtml($menuItem['name']) . '</span>';
            $html .= '</a>';

            // Arrow button for subcategories
            if ($hasChildren) {
                $html .= '<button @click="openSubcategory(\'' . $currentIndex . '\')"';
                $html .= ' class="absolute right-0 flex items-center justify-center w-11 h-11 mr-8 cursor-pointer bg-container-lighter border-container"';
                $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Open %1 subcategories', $menuItem['name'])) . '"';
                $html .= ' aria-haspopup="true"';
                $html .= ' :aria-expanded="mobilePanelActiveId === \'' . $currentIndex . '\'">';
                $html .= '<div class="w-8 h-8 border rounded">';
                $html .= $heroicons->chevronRightHtml('w-full h-full p-1', $arrowSize, $arrowSize, ["aria-hidden" => "true"]);
                $html .= '</div></button>';
            }

            $html .= '</div>';

            // Subcategory panel (recursive)
            if ($hasChildren) {
                $html .= '<div data-child-id="' . $escaper->escapeHtmlAttr($currentIndex) . '"';
                $html .= ' class="absolute top-0 right-0 z-' . $zIndex . ' flex flex-col gap-1 w-full h-full p-1 bg-container-lighter hidden"';
                $html .= ' :class="{ \'hidden\': mobilePanelActiveId !== \'' . $currentIndex . '\', \'flex\': mobilePanelActiveId === \'' . $currentIndex . '\' }">';

                $html .= '<ul class="mt-16 transition-transform duration-200 ease-in-out translate-x-full transform"';
                $html .= ' :class="{ \'translate-x-full\' : mobilePanelActiveId !== \'' . $currentIndex . '\', \'translate-x-0\' : mobilePanelActiveId === \'' . $currentIndex . '\' }"';
                $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Subcategories')) . '">';

                // Back button
                $backAction = $level === 0 ? 'backToMainCategories(\'' . $currentIndex . '-main\')' : 'backToParentCategory(\'' . $parentIndex . '\')';
                $backLabel = $level === 0 ? __('Back to main categories') : __('Back to %1', $menuItem['name']);

                $html .= '<li><button type="button"';
                $html .= ' class="flex items-center px-8 py-4 border-b cursor-pointer bg-container border-container w-full border-t"';
                $html .= ' @click="' . $backAction . '"';
                $html .= ' aria-label="' . $escaper->escapeHtmlAttr($backLabel) . '">';
                $html .= $heroicons->chevronLeftHtml('', 24, 24, ["aria-hidden" => "true"]);
                $html .= '<span class="ml-4">' . $escaper->escapeHtml($menuItem['name']) . '</span>';
                $html .= '</button></li>';

                // View All link
                $html .= '<li><a href="' . $escaper->escapeUrl($menuItem['url']) . '"';
                $html .= ' title="' . $escaper->escapeHtmlAttr($menuItem['name']) . '"';
                $html .= ' class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container">';
                $html .= '<span class="ml-10">' . $escaper->escapeHtml(__('View All')) . '</span>';
                $html .= '</a></li>';

                // Recursively render child items
                $html .= renderMobileMenuItems($menuItem['childData'], $heroicons, $escaper, $level + 1, $currentIndex);

                $html .= '</ul></div>';
            }

            $html .= '</li>';
        }

        return $html;
    }
}

// Get custom menu items from variables (same as desktop template)
$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
/** @var VariableFactory $variableFactory */
$variableModel = $objectManager->create(\Magento\Variable\Model\Variable::class);

$fhrUrlSparesParts = $variableModel->loadByCode('fhr_url_spare_parts')->getPlainValue();
$fhrUrlAccessories = $variableModel->loadByCode('fhr_url_accessories')->getPlainValue();
$fhrUrlUsedPhones = $variableModel->loadByCode('fhr_url_used_phones')->getPlainValue();
$fhrUrlBargainCorner = $variableModel->loadByCode('fhr_url_bargain_corner')->getPlainValue();
?>
<div class="navigation hidden lg:flex">
    <?php if($html) :?>
        <div class="sections nav-sections">
            <nav class="navigation" data-action="navigation">
                <?= $html ?>
            </nav>
        </div>
    <?php else: ?>
        <?= $this->getMagentoTopMenuHtml() ?>
    <?php endif; ?>
</div>
<nav
    x-data="initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?>()"
    @load.window="setActiveMenu($root)"
    @keydown.window.escape="closeMenu()"
    class="z-20 navigation lg:hidden size-12"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation')) ?>"
    role="navigation"
>
    <!-- mobile -->
    <button
        x-ref="mobileMenuTrigger"
        @click="openMenu()"
        :class="{'-ml-2 overflow-x-hidden overflow-y-auto fixed top-0 -left-2 w-full' : open}"
        type="button"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Open menu')) ?>"
        aria-haspopup="menu"
        :aria-expanded="open"
        :hidden="open"
    >
        <?= $heroicons->menuHtml('p-2', 48, 48, [":class" => "{ 'hidden' : open, 'block': !open }", "aria-hidden" => "true"]) ?>
    </button>
    <div
        x-ref="mobileMenuNavLinks"
        class="
            fixed top-0 right-0 w-full h-full flex
            flex-col border-t border-container bg-container-lighter
            overflow-y-auto overflow-x-hidden
        "
        :class="{ 'hidden' : !open }"
        :aria-hidden="open ? 'false' : 'true'"
        role="dialog"
        aria-modal="true"
    >
        <?php if($html): ?>
            <!-- NinjaMenus mobile menu with categories -->
            <div class="sections nav-sections border-t flex flex-col gap-y-1 mt-16">
                <nav class="navigation" data-action="navigation">
                    <?= $html ?>
                </nav>
            </div>
        <?php else: ?>
            <!-- Fallback: Standard Hyva mobile menu with categories (enhanced with multi-level support) -->
            <ul
                class="border-t flex flex-col gap-y-1 mt-16"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation links')) ?>"
            >
                <?= /* @noEscape */ renderMobileMenuItems($menuItems, $heroicons, $escaper, 0, '') ?>
            </ul>
        <?php endif; ?>

        <!-- Custom menu items for mobile (always show at the end) -->
        <div class="custom-menu-items flex flex-col gap-y-1 mt-4 px-4">
            <a href="<?= $escaper->escapeUrl($fhrUrlSparesParts) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Spares parts')) ?>
            </a>
            <a href="<?= $escaper->escapeUrl($fhrUrlAccessories) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Accessories')) ?>
            </a>
            <a href="<?= $escaper->escapeUrl($fhrUrlUsedPhones) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Used Phones')) ?>
            </a>
            <a href="<?= $escaper->escapeUrl($fhrUrlBargainCorner) ?>"
               class="flex items-center w-full px-4 py-3 border-b cursor-pointer bg-container-lighter border-container text-base text-gray-700 hover:bg-gray-100">
                <?= $escaper->escapeHtml(__('Bargain corner')) ?>
            </a>
        </div>

        <button
            @click="closeMenu()"
            class="fixed top-0 right-0 flex justify-end w-16 self-end mb-1 w-full border-b z-[100] bg-cgrey-0"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Close menu')) ?>"
            type="button"
        >
            <?= $heroicons->xHtml('hidden p-4', 64, 64, [":class" => "{ 'hidden' : !open, 'block': open }", "aria-hidden" => "true"]) ?>
        </button>
    </div>
</nav>
<script>
    'use strict';

    const initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            mobilePanelActiveId: null,
            open: false,
            navigationHistory: [], // Track navigation history for breadcrumbs
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('underline');
                    item.closest('li.level-0') &&
                    item.closest('li.level-0').querySelector('a.level-0').classList.add('underline');
                });
            },
            openMenu() {
                this.open = true
                this.mobilePanelActiveId = null
                this.navigationHistory = []
                this.$nextTick(() => hyva.trapFocus(this.$refs['mobileMenuNavLinks']));
                // Prevent from body scrolling while mobile menu opened
                document.body.style.position = 'fixed';
            },
            closeMenu() {
                document.body.style.position = '';

                if (this.open) {
                    this.$nextTick(() => this.$refs['mobileMenuTrigger'].focus() || hyva.releaseFocus());
                }

                this.open = false
                this.mobilePanelActiveId = null
                this.navigationHistory = []
            },
            openSubcategory(index) {
                console.log('Opening subcategory:', index);
                console.log('Current mobilePanelActiveId:', this.mobilePanelActiveId);

                // Add current panel to history if we're not already at this level
                if (this.mobilePanelActiveId && this.mobilePanelActiveId !== index) {
                    this.navigationHistory.push(this.mobilePanelActiveId)
                }

                const menuNodeRef = document.querySelector('[data-child-id="' + index + '"]')
                console.log('Found menu node:', menuNodeRef);

                this.mobilePanelActiveId = index
                console.log('Set mobilePanelActiveId to:', this.mobilePanelActiveId);

                this.$nextTick(() => {
                    if (menuNodeRef) {
                        hyva.trapFocus(menuNodeRef)
                    }
                })
            },
            backToMainCategories(index) {
                // Reset to main menu
                this.mobilePanelActiveId = null
                this.navigationHistory = []
                const menuNodeRef = document.querySelector('[data-child-id="' + index + '"]')
                this.$nextTick(() => {
                    hyva.trapFocus(this.$refs['mobileMenuNavLinks'])
                    if (menuNodeRef && menuNodeRef.querySelector('a')) {
                        menuNodeRef.querySelector('a').focus()
                    }
                })
            },
            backToParentCategory(parentIndex) {
                // Navigate back to parent category
                this.mobilePanelActiveId = parentIndex
                // Remove the last item from history if it exists
                if (this.navigationHistory.length > 0) {
                    this.navigationHistory.pop()
                }

                const menuNodeRef = document.querySelector('[data-child-id="' + parentIndex + '"]')
                this.$nextTick(() => {
                    if (menuNodeRef) {
                        hyva.trapFocus(menuNodeRef)
                    }
                })
            }
        }
    }
</script>

<script>
    loadScript('<?= $block->getViewFileUrl('Hyva_MagezonBuilder::js/jquery.min.js') ?>', 'jquery')
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Magezon_NinjaMenus::js/jquery.drilldown.min.js') ?>', 'drilldown');
        })
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Magezon_NinjaMenus::js/jquery.hoverIntent.min.js') ?>', 'hoverIntent');
        })
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Magezon_Core::js/jquery-scrolltofixed-min.js') ?>', 'scrolltofixed');
        })
        .then(() => {
            return loadScript('<?= $block->getViewFileUrl('Hyva_MagezonNinjaMenus::js/menu.js') ?>', 'menu');
        })
        .then(() => {
            const menuElement = $(".ninjamenus");
            const options = {
                "mobileBreakpoint": 1024,
                "stick": true,
            };
            let ninjaMenus = new NinjaMenus(menuElement, options);
        })
</script>
