<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var Navigation $viewModelNavigation */
$viewModelNavigation = $viewModels->require(Navigation::class, $block);

$uniqueId = '_' . uniqid();

// Order is important here: 1. build the menu data, 2. then set the cache tags from the view model identities
$maxLevel = 4;
// Increase depth to match desktop menu (6 levels total)
$menuItems = $viewModelNavigation->getNavigation(2 + $maxLevel);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

// Recursive function to render mobile menu items with all levels
function renderMobileMenuItems($items, $level = 0, $parentIndex = '', $heroicons, $escaper) {
    if (empty($items)) return '';

    $html = '';
    foreach ($items as $index => $menuItem) {
        $currentIndex = $parentIndex ? $parentIndex . '-' . $index : $index;
        $hasChildren = !empty($menuItem['childData']);

        $html .= '<li data-child-id="' . $escaper->escapeHtmlAttr($currentIndex) . '-main" class="level-' . $level . '">';
        $html .= '<div class="flex items-center transition-transform duration-150 ease-in-out transform"';
        $html .= ' :class="{ \'-translate-x-full\' : mobilePanelActiveId && mobilePanelActiveId !== \'' . $currentIndex . '\', \'translate-x-0\' : !mobilePanelActiveId || mobilePanelActiveId === \'' . $currentIndex . '\' }">';

        // Menu item link
        $html .= '<a class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container level-' . $level . '"';
        $html .= ' href="' . $escaper->escapeUrl($menuItem['url']) . '"';
        $html .= ' title="' . $escaper->escapeHtmlAttr($menuItem['name']) . '">';
        $html .= '<span class="ml-' . ($level * 4) . '">' . $escaper->escapeHtml($menuItem['name']) . '</span>';
        $html .= '</a>';

        // Arrow button for subcategories
        if ($hasChildren) {
            $html .= '<button @click="openSubcategory(\'' . $currentIndex . '\')"';
            $html .= ' class="absolute right-0 flex items-center justify-center w-11 h-11 mr-8 cursor-pointer bg-container-lighter border-container"';
            $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Open %1 subcategories', $menuItem['name'])) . '"';
            $html .= ' aria-haspopup="true"';
            $html .= ' :aria-expanded="mobilePanelActiveId === \'' . $currentIndex . '\'">';
            $html .= '<div class="w-8 h-8 border rounded">';
            $html .= $heroicons->chevronRightHtml('w-full h-full p-1', 24, 24, ["aria-hidden" => "true"]);
            $html .= '</div></button>';
        }

        $html .= '</div>';

        // Subcategory panel
        if ($hasChildren) {
            $html .= '<div data-child-id="' . $escaper->escapeHtmlAttr($currentIndex) . '"';
            $html .= ' class="absolute top-0 right-0 z-10 flex flex-col gap-1 w-full h-full p-1 bg-container-lighter"';
            $html .= ' :class="{ \'hidden\': mobilePanelActiveId !== \'' . $currentIndex . '\' }">';

            $html .= '<ul class="mt-16 transition-transform duration-200 ease-in-out translate-x-full transform"';
            $html .= ' :class="{ \'translate-x-full\' : mobilePanelActiveId !== \'' . $currentIndex . '\', \'translate-x-0\' : mobilePanelActiveId === \'' . $currentIndex . '\' }"';
            $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Subcategories')) . '">';

            // Back button
            $html .= '<li><button type="button"';
            $html .= ' class="flex items-center px-8 py-4 border-b cursor-pointer bg-container border-container w-full border-t"';
            $html .= ' @click="backToMainCategories(\'' . $currentIndex . '-main\')"';
            $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Back to main categories')) . '">';
            $html .= $heroicons->chevronLeftHtml('', 24, 24, ["aria-hidden" => "true"]);
            $html .= '<span class="ml-4">' . $escaper->escapeHtml($menuItem['name']) . '</span>';
            $html .= '</button></li>';

            // View All link
            $html .= '<li><a href="' . $escaper->escapeUrl($menuItem['url']) . '"';
            $html .= ' title="' . $escaper->escapeHtmlAttr($menuItem['name']) . '"';
            $html .= ' class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container">';
            $html .= '<span class="ml-10">' . $escaper->escapeHtml(__('View All')) . '</span>';
            $html .= '</a></li>';

            // Render child items recursively
            foreach ($menuItem['childData'] as $subIndex => $subMenuItem) {
                $subCurrentIndex = $currentIndex . '-' . $subIndex;
                $subHasChildren = !empty($subMenuItem['childData']);

                $html .= '<li>';
                if ($subHasChildren) {
                    // Subcategory with children - make it clickable to expand
                    $html .= '<div class="flex items-center">';
                    $html .= '<a href="' . $escaper->escapeUrl($subMenuItem['url']) . '"';
                    $html .= ' title="' . $escaper->escapeHtmlAttr($subMenuItem['name']) . '"';
                    $html .= ' class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container">';
                    $html .= '<span class="ml-10 text-base text-gray-700">' . $escaper->escapeHtml($subMenuItem['name']) . '</span>';
                    $html .= '</a>';
                    $html .= '<button @click="openSubcategory(\'' . $subCurrentIndex . '\')"';
                    $html .= ' class="absolute right-0 flex items-center justify-center w-11 h-11 mr-8 cursor-pointer bg-container-lighter border-container"';
                    $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Open %1 subcategories', $subMenuItem['name'])) . '">';
                    $html .= '<div class="w-6 h-6 border rounded">';
                    $html .= $heroicons->chevronRightHtml('w-full h-full p-0.5', 16, 16, ["aria-hidden" => "true"]);
                    $html .= '</div></button>';
                    $html .= '</div>';

                    // Render deeper level panel
                    $html .= '<div data-child-id="' . $escaper->escapeHtmlAttr($subCurrentIndex) . '"';
                    $html .= ' class="absolute top-0 right-0 z-20 flex flex-col gap-1 w-full h-full p-1 bg-container-lighter"';
                    $html .= ' :class="{ \'hidden\': mobilePanelActiveId !== \'' . $subCurrentIndex . '\' }">';

                    $html .= '<ul class="mt-16 transition-transform duration-200 ease-in-out translate-x-full transform"';
                    $html .= ' :class="{ \'translate-x-full\' : mobilePanelActiveId !== \'' . $subCurrentIndex . '\', \'translate-x-0\' : mobilePanelActiveId === \'' . $subCurrentIndex . '\' }">';

                    // Back button for deeper level
                    $html .= '<li><button type="button"';
                    $html .= ' class="flex items-center px-8 py-4 border-b cursor-pointer bg-container border-container w-full border-t"';
                    $html .= ' @click="backToParentCategory(\'' . $currentIndex . '\')"';
                    $html .= ' aria-label="' . $escaper->escapeHtmlAttr(__('Back to %1', $menuItem['name'])) . '">';
                    $html .= $heroicons->chevronLeftHtml('', 24, 24, ["aria-hidden" => "true"]);
                    $html .= '<span class="ml-4">' . $escaper->escapeHtml($subMenuItem['name']) . '</span>';
                    $html .= '</button></li>';

                    // View All for subcategory
                    $html .= '<li><a href="' . $escaper->escapeUrl($subMenuItem['url']) . '"';
                    $html .= ' class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container">';
                    $html .= '<span class="ml-10">' . $escaper->escapeHtml(__('View All')) . '</span>';
                    $html .= '</a></li>';

                    // Render grandchildren
                    foreach ($subMenuItem['childData'] as $grandChild) {
                        $html .= '<li><a href="' . $escaper->escapeUrl($grandChild['url']) . '"';
                        $html .= ' title="' . $escaper->escapeHtmlAttr($grandChild['name']) . '"';
                        $html .= ' class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container">';
                        $html .= '<span class="ml-14 text-sm text-gray-600">' . $escaper->escapeHtml($grandChild['name']) . '</span>';
                        $html .= '</a></li>';
                    }

                    $html .= '</ul></div>';
                } else {
                    // Simple subcategory without children
                    $html .= '<a href="' . $escaper->escapeUrl($subMenuItem['url']) . '"';
                    $html .= ' title="' . $escaper->escapeHtmlAttr($subMenuItem['name']) . '"';
                    $html .= ' class="flex items-center w-full px-8 py-4 border-b cursor-pointer bg-container-lighter border-container">';
                    $html .= '<span class="ml-10 text-base text-gray-700">' . $escaper->escapeHtml($subMenuItem['name']) . '</span>';
                    $html .= '</a>';
                }
                $html .= '</li>';
            }

            $html .= '</ul></div>';
        }

        $html .= '</li>';
    }

    return $html;
}

?>
<nav
    x-data="initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?>()"
    @load.window="setActiveMenu($root)"
    @keydown.window.escape="closeMenu()"
    class="z-20 order-1 sm:order-1 lg:order-2 navigation lg:hidden w-12 h-12"
    aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation')) ?>"
    role="navigation"
>
    <!-- mobile -->
    <button
        x-ref="mobileMenuTrigger"
        @click="openMenu()"
        :class="{'overflow-x-hidden overflow-y-auto fixed top-0 left-0 w-full' : open}"
        type="button"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Open menu')) ?>"
        aria-haspopup="menu"
        :aria-expanded="open"
        :hidden="open"
    >
        <?= $heroicons->menuHtml('p-3', 48, 48, [":class" => "{ 'hidden' : open, 'block': !open }", "aria-hidden" => "true"]) ?>
    </button>
    <div
        x-ref="mobileMenuNavLinks"
        class="
            fixed top-0 right-0 w-full h-full p-1 hidden
            flex-col border-t border-container bg-container-lighter
            overflow-y-auto overflow-x-hidden
        "
        :class="{ 'flex': open, 'hidden': !open }"
        :aria-hidden="open ? 'false' : 'true'"
        role="dialog"
        aria-modal="true"
    >
        <ul
            class="border-t flex flex-col gap-y-1 mt-16"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Site navigation links')) ?>"
        >
            <?= /* @noEscape */ renderMobileMenuItems($menuItems, 0, '', $heroicons, $escaper) ?>
        </ul>
        <button
            @click="closeMenu()"
            class="absolute flex justify-end w-16 self-end mb-1"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Close menu')) ?>"
            type="button"
        >
            <?= $heroicons->xHtml('hidden p-4', 64, 64, [":class" => "{ 'hidden' : !open, 'block': open }", "aria-hidden" => "true"]) ?>
        </button>
    </div>
</nav>
<script>
    'use strict';

    const initMenuMobile<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            mobilePanelActiveId: null,
            open: false,
            navigationHistory: [], // Track navigation history for breadcrumbs
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('underline');
                    item.closest('li.level-0') &&
                    item.closest('li.level-0').querySelector('a.level-0').classList.add('underline');
                });
            },
            openMenu() {
                this.open = true
                this.mobilePanelActiveId = null
                this.navigationHistory = []
                this.$nextTick(() => hyva.trapFocus(this.$refs['mobileMenuNavLinks']));
                // Prevent from body scrolling while mobile menu opened
                document.body.style.position = 'fixed';
            },
            closeMenu() {
                document.body.style.position = '';

                if (this.open) {
                    this.$nextTick(() => this.$refs['mobileMenuTrigger'].focus() || hyva.releaseFocus());
                }

                this.open = false
                this.mobilePanelActiveId = null
                this.navigationHistory = []
            },
            openSubcategory(index) {
                // Add current panel to history if we're not already at this level
                if (this.mobilePanelActiveId && this.mobilePanelActiveId !== index) {
                    this.navigationHistory.push(this.mobilePanelActiveId)
                }

                const menuNodeRef = document.querySelector('[data-child-id="' + index + '"]')
                this.mobilePanelActiveId = index
                this.$nextTick(() => {
                    if (menuNodeRef) {
                        hyva.trapFocus(menuNodeRef)
                    }
                })
            },
            backToMainCategories(index) {
                // Reset to main menu
                this.mobilePanelActiveId = null
                this.navigationHistory = []
                const menuNodeRef = document.querySelector('[data-child-id="' + index + '"]')
                this.$nextTick(() => {
                    hyva.trapFocus(this.$refs['mobileMenuNavLinks'])
                    if (menuNodeRef && menuNodeRef.querySelector('a')) {
                        menuNodeRef.querySelector('a').focus()
                    }
                })
            },
            backToParentCategory(parentIndex) {
                // Navigate back to parent category
                this.mobilePanelActiveId = parentIndex
                // Remove the last item from history if it exists
                if (this.navigationHistory.length > 0) {
                    this.navigationHistory.pop()
                }

                const menuNodeRef = document.querySelector('[data-child-id="' + parentIndex + '"]')
                this.$nextTick(() => {
                    if (menuNodeRef) {
                        hyva.trapFocus(menuNodeRef)
                    }
                })
            }
        }
    }
</script>
