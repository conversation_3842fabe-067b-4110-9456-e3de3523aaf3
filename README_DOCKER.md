# How install docker containers for FHR

## Get code
```shell
<NAME_EMAIL>:fhr/fhr-mobileronline.git
```
OR
```shell
git clone https://git-dev01.inkclub.local/fhr/fhr-mobileronline.git
```

## Create app/etc/env.php
```php
<?php
return [
    'backend' => [
        'frontName' => 'fhrcp'
    ],
    'crypt' => [
        'key' => '75413f8579a084f7a6ac663a7722f27b'
    ],
    'db' => [
        'table_prefix' => '',
        'connection' => [
            'default' => [
                'host' => 'fhr-db',
                'dbname' => 'magento2',
                'username' => 'magento2',
                'password' => 'magento2',
                'active' => '1',
                'driver_options' => [
                    1002 => 'SET NAMES utf8'
                ]
            ]
        ]
    ],
    'resource' => [
        'default_setup' => [
            'connection' => 'default'
        ]
    ],
    'x-frame-options' => 'SAMEORIGIN',
    'MAGE_MODE' => 'developer',
    'session' => [
        'save' => 'redis',
        'redis' => [
            'host' => 'fhr-redis',
            'port' => '6380',
            'password' => '',
            'timeout' => '2.5',
            'persistent_identifier' => '',
            'database' => '0',
            'compression_threshold' => '2048',
            'compression_library' => 'gzip',
            'log_level' => '1',
            'max_concurrency' => '6',
            'break_after_frontend' => '5',
            'break_after_adminhtml' => '30',
            'first_lifetime' => '600',
            'bot_first_lifetime' => '60',
            'bot_lifetime' => '7200',
            'disable_locking' => '0',
            'min_lifetime' => '60',
            'max_lifetime' => '2592000'
        ]
    ],
    'cache' => [
        'frontend' => [
            'default' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => 'fhr-redis',
                    'port' => '6380',
                    'persistent' => '',
                    'database' => '1',
                    'password' => '',
                    'force_standalone' => '0',
                    'connect_retries' => '1',
                    'read_timeout' => '10',
                    'automatic_cleaning_factor' => '0',
                    'compress_data' => '1',
                    'compress_tags' => '1',
                    'compress_threshold' => '2048',
                    'compression_lib' => 'gzip'
                ]
            ],
            'page_cache' => [
                'backend' => 'Cm_Cache_Backend_Redis',
                'backend_options' => [
                    'server' => 'fhr-redis',
                    'port' => '6380',
                    'persistent' => '',
                    'database' => '2',
                    'password' => '',
                    'force_standalone' => '0',
                    'connect_retries' => '1',
                    'lifetimelimit' => '57600',
                    'compress_data' => '0'
                ]
            ]
        ]
    ],
    'system' => [
        'default' => [
            'dev' => [
                'js' => [
                    'session_storage_key' => 'collected_errors'
                ]
            ]
        ]
    ],
    'install' => [
        'date' => 'Wed, 15 Mar 2023 08:00:00 +0000'
    ],
    'queue' => [
        'amqp' => [
            'host' => 'fhr-rabbitmq',
            'port' => '5672',
            'user' => 'guest',
            'password' => 'guest',
            'virtualhost' => '/'
        ]
    ],
    'elasticsearch' => [
        'host' => 'elasticsearch.fhr.docker',
        'port' => '9200'
    ],
    'cache_types' => [
        'compiled_config' => 1
    ]
];
```

## Get DB. Unpack and remove DEFINER from sql
```shell
rsync --progress -avHe ssh magento@**************:/var/www/fhr.se/var/backups/fhr_PROD_last.sql.bz2 /home/<USER>/Downloads/fhr_PROD.sql.bz2
bzcat ~/Downloads/fhr_PROD.sql.bz2 > ~/Downloads/fhr_PROD.sql
sed -i 's/DEFINER[ ]*=[ ]*[^*]*\*/\*/' ~/Downloads/fhr_PROD.sql

#for MAC
LC_ALL=C sed -i '' 's/DEFINER[ ]*=[ ]*[^*]*\*/\*/' ~/Downloads/fhr_PROD.sql
```

## Add local DNS to /etc/hosts file
```text
127.0.0.1 fhr.docker
127.0.0.1 db.fhr.docker
127.0.0.1 redis.fhr.docker
127.0.0.1 elasticsearch.fhr.docker
127.0.0.1 fpm.fhr.docker
127.0.0.1 web.fhr.docker
127.0.0.1 varnish.fhr.docker
127.0.0.1 tls.fhr.docker
127.0.0.1 rabbitmq.fhr.docker
127.0.0.1 mailhog.fhr.docker
127.0.0.1 fhrm24.local
127.0.0.1 fhrpartsm24.local
```
### for Olegh congig docker-compose.olegh.yml
```shell
docker-compose -f docker-compose.olegh.yml up -d
```
```text
********** fhr.docker
********** db.fhr.docker
********** redis.fhr.docker
********** elasticsearch.fhr.docker
********** fpm.fhr.docker
********** web.fhr.docker
********** varnish.fhr.docker
********** tls.fhr.docker
********** rabbitmq.fhr.docker
*********** mailhog.fhr.docker
********** fhrm24.local
********** fhrpartsm24.local
```

## Build and run docker containers
```shell
docker-compose build
docker-compose up -d
```

## Load fhr_PROD.sql into fhr-db container
```shell
mysql -h db.fhr.docker -P 3307 -u magento2 -pmagento2 magento2 < ~/Downloads/fhr_PROD.sql

# Alternative: place the SQL backup file to ./docker-mariadb/backups directory and import from inside db.fhr.docker container using this scripts
mysql -h localhost -P 3306 -u magento2 -pmagento2 magento2 < /var/backups/fhr_PROD.sql

# Alternative 2 Jainul
docker exec -ti fhr-mobileronline_fhr-db_1 mysql -h localhost -P 3306 -u magento2 -pmagento2 magento2 < ../fhr-backups/fhr_PROD.sql
```

## Check connection to mariaDB server and execute
```
docker-mariadb/sql/after_db_load_dump_from_production.sql
```

## Execute Composer install from console in source folder
```shell
composer install
```

## Go to fhr-fpm container folder /var/www/html and run
```shell
docker exec -ti fhr-mobileronline_fhr-fpm_1 /bin/bash
```
```shell
php bin/magento se:up
php bin/magento se:di:co
php bin/magento ind:res
php bin/magento ind:rei
php bin/magento c:f  => flushes cache in Magento
```

## Site should work on local links
 - https://fhrm24.local
 - http://fhrm24.local
 - https://fhrpartsm24.local
 - http://fhrpartsm24.local

## Admin panel link
 - https://fhrm24.local/fhrcp

## Media sync
```shell
rsync -azPh magento@**************:/var/www/fhr.se/pub/media /home/<USER>/www/mobileronline.local/pub --exclude=catalog/product/cache
```

## Update media path after media sync
```sql
from PROD:
UPDATE core_config_data SET value = 'http://fhr.se/pub/media/' WHERE path = 'web/unsecure/base_media_url';
UPDATE core_config_data SET value = 'https://fhr.se/pub/media/' WHERE path = 'web/secure/base_media_url';

to LOCAL:
UPDATE core_config_data SET value = 'http://fhrm24.local/pub/media/' WHERE path = 'web/unsecure/base_media_url';
UPDATE core_config_data SET value = 'https://fhrm24.local/pub/media/' WHERE path = 'web/secure/base_media_url';
```

## Password reset utility
```
php n98-magerun2.phar admin:user:change-password
```