#!/bin/bash

cat .job_status

JOB_VAR=$(cat .job_status)

if [ -z "$JOB_VAR" ];  
then
  echo '
  (         (   (       )     )   *           )          (            (   (       (       
  )\ )      )\ ))\ ) ( /(  ( /( (  `       ( /(  *   )   )\ )   (     )\ ))\ )    )\ )    
  (()/(  (  (()/(()/( )\()) )\()))\))(  (   )\()` )  /(  (()/(   )\   (()/(()/( ( (()/(    
  /(_)) )\  /(_)/(_)((_)\ ((_)\((_)()\ )\ ((_)\ ( )(_))  /(_)((((_)(  /(_)/(_)))\ /(_))   
  (_))_ ((_)(_))(_))   ((___ ((_(_()((_((_) _((_(_(_())  (_))_|)\ _ )\(_))(_)) ((_(_))_    
  |   \| __| _ | |   / _ \ \ / |  \/  | __| \| |_   _|  | |_  (_)_\(_|_ _| |  | __|   \   
  | |) | _||  _| |__| (_) \ V /| |\/| | _|| .` | | |    | __|  / _ \  | || |__| _|| |) |  
  |___/|___|_| |____|\___/ |_| |_|  |_|___|_|\_| |_|    |_|   /_/ \_\|___|____|___|___/ 
  '
else
  echo '
   ___  _______  __  ______  ____  ________  ________  _________  __  ______  __   _________________ 
  / _ \/ __/ _ \/ / / __ \ \/ /  |/  / __/ |/ /_  __/ / ___/ __ \/  |/  / _ \/ /  / __/_  __/ __/ _ \
 / // / _// ___/ /_/ /_/ /\  / /|_/ / _//    / / /   / /__/ /_/ / /|_/ / ___/ /__/ _/  / / / _// // /
/____/___/_/  /____|____/ /_/_/  /_/___/_/|_/ /_/    \___/\____/_/  /_/_/  /____/___/ /_/ /___/____/                                                                                                     
  '
fi
