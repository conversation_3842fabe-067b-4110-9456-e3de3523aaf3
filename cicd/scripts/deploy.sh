#!/bin/bash

echo "Executing deploy.ssh script..."

ssh "$SSH_USERNAME"@"$SSH_HOSTNAME" "mkdir -p ~/artifact/"
echo "Executing deploy.ssh script -> done mkdir"

ssh "$SSH_USERNAME"@"$SSH_HOSTNAME" "rm -rf ~/artifact/*"
echo "Executing deploy.ssh script -> done rm"

scp -r deploy.tar.gz "$SSH_USERNAME"@"$SSH_HOSTNAME":~/artifact/
ssh "$SSH_USERNAME"@"$SSH_HOSTNAME" "cd ~/artifact && tar -xf deploy.tar.gz && rm -f deploy.tar.gz"
echo "Executing deploy.ssh script -> done cd"

ssh "$SSH_USERNAME"@"$SSH_HOSTNAME" "/bin/sh ~/artifact/cicd/scripts/pre_magento_deploy.sh $MAGENTO_ROOT_DIR"
echo "Executing deploy.ssh script -> done pre_magento_deploy.sh"

ssh "$SSH_USERNAME"@"$SSH_HOSTNAME" "/bin/sh ~/artifact/cicd/scripts/magento_deploy.sh $MAGENTO_ROOT_DIR"
echo "Executing deploy.ssh script -> done magento_deploy.sh"

ssh "$REDIS_USERNAME"@"$REDIS_HOSTNAME" "redis-cli flushall"
echo "Executing deploy.ssh script -> done edis-cli flushall"

echo "Finished deploy.ssh script"
