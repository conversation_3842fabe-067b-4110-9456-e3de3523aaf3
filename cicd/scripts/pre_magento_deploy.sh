#!/bin/bash
set -x

MAGENTO_ROOT_DIR=$1

sudo chown gitlab-runner:magento -R /var/www/magento

sudo chmod 0777 -R /var/www/magento

# Disable CRON & Node 1 Node 2 Pub syncing
sudo mv -vi /var/spool/cron/crontabs/magento-cli /var/spool/cron/crontabs/magento-cli.disabled
# Stop php-fpm
sudo systemctl stop php8.1-fpm
# Stop nginx
sudo systemctl stop nginx


if [ -d "$MAGENTO_ROOT_DIR"-release ]; then sudo cp -Rf "$MAGENTO_ROOT_DIR"-release/pub/media "$MAGENTO_ROOT_DIR"/pub/; fi
if [ -d "$MAGENTO_ROOT_DIR"-release ]; then sudo cp -Rf "$MAGENTO_ROOT_DIR"-release/var "$MAGENTO_ROOT_DIR"; fi

mv "$MAGENTO_ROOT_DIR"-release "$MAGENTO_ROOT_DIR"

sleep 5

cd "$MAGENTO_ROOT_DIR" || exit 1
bin/magento maintenance:enable

mkdir -p "$MAGENTO_ROOT_DIR"-release
cp -Rf ~/artifact/* "$MAGENTO_ROOT_DIR"-release

#only for env.php file backup
mkdir -p "$MAGENTO_ROOT_DIR"-bkup-files
cp -f "$MAGENTO_ROOT_DIR"/app/etc/env.php "$MAGENTO_ROOT_DIR"-bkup-files/env.php

# Restore env.php
cp -f "$MAGENTO_ROOT_DIR"-bkup-files/env.php "$MAGENTO_ROOT_DIR"-release/app/etc/env.php

# Restore auth.json
#cp -f "$MAGENTO_ROOT_DIR"/auth.json "$MAGENTO_ROOT_DIR"-release/auth.json

# Restore pub/media
cp -Rf "$MAGENTO_ROOT_DIR"/pub/media "$MAGENTO_ROOT_DIR"-release/pub/

# Restore var
cp -Rf "$MAGENTO_ROOT_DIR"/var "$MAGENTO_ROOT_DIR"-release

cd "$MAGENTO_ROOT_DIR"-release || exit 1

composer install --no-cache --no-interaction || echo "" > .job_status && exit 1

# bin/magento maintenance:enable
