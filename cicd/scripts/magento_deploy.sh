#!/bin/bash
set -x

MAGENTO_ROOT_DIR=$1

# mv "$MAGENTO_ROOT_DIR" "$MAGENTO_ROOT_DIR"-temp
sudo chmod -R 0777 "$MAGENTO_ROOT_DIR"

sudo rm -rfd "$MAGENTO_ROOT_DIR" || exit 1
sudo sleep 5

mv "$MAGENTO_ROOT_DIR"-release/ "$MAGENTO_ROOT_DIR"
sudo sleep 5

sudo rm -rfd "$MAGENTO_ROOT_DIR"/magento-release

sudo chmod -R 0777 "$MAGENTO_ROOT_DIR"
sudo chown gitlab-runner:magento -R "$MAGENTO_ROOT_DIR"

cd "$MAGENTO_ROOT_DIR" || exit 1

sudo bin/magento maintenance:enable
sudo rm -rfd /var/www/magento/generated/code/*
sudo rm -rfd /var/www/magento/generated/metadata/*

sudo php bin/magento setup:upgrade || exit 1
date
sudo du -sh /var/www/magento/generated/
date
sudo php bin/magento setup:di:compile || exit 1

sudo bin/magento c:c
sudo bin/magento c:f

sudo bin/magento deploy:mode:set production --skip-compilation

# one time activity install globally
#sudo apt-get update
# sudo apt-get install nodejs -y
# sudo apt-get install npm -y

# innitialize requirejs & uglify
sudo npm install requirejs
sudo npm install uglify-js2


## Apply Patch ##

## Apply Patch ##

## Install product feed extension dependency ##
composer config --no-plugins allow-plugins.magento/magento-composer-installer false
composer config --no-plugins allow-plugins.magento/inventory-composer-installer false
composer config --no-plugins allow-plugins.laminas/laminas-dependency-plugin false
composer config --no-plugins allow-plugins.dealerdirect/phpcodesniffer-composer-installer false
composer config --no-plugins allow-plugins.magento/composer-dependency-version-audit-plugin false
composer config --no-plugins allow-plugins.magento/composer-root-update-plugin false

composer require zf1/zend-exception
composer require zf1/zend-reflection
## Install product feed extension dependency ##

sudo bin/magento maintenance:disable

sudo chown magento-cli:magento -R /var/www/magento
# sudo rm -rf "$MAGENTO_ROOT_DIR"-temp

# Enable CRON & Node 1 Node 2 Pub syncing
sudo mv -vi /var/spool/cron/crontabs/magento-cli.disabled /var/spool/cron/crontabs/magento-cli

sudo systemctl restart php8.1-fpm
sudo systemctl restart nginx
