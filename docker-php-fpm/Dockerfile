# Use the official PHP image as a parent image
# FROM php:8.2-fpm
FROM php:8.1-fpm

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libzip-dev \
    libonig-dev \
    libxml2-dev \
    libxslt-dev \
    libicu-dev \
    libgmp-dev \
    default-mysql-client \
    unzip \
    git \
    curl \
    cron \
    sudo \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd intl zip bcmath pdo_mysql opcache soap xsl gmp sockets

# Install Xdebug version 2.9.8, compatible with PHP 7.3
RUN if ! pecl list | grep -q xdebug; then \
    pecl install xdebug-3.2.2 && docker-php-ext-enable xdebug; \
    else echo "Xdebug is already installed"; \
fi

# Install Composer and switch to version 1
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Copy custom php.ini configuration
COPY php.ini /usr/local/etc/php/

# Set working directory to /var/www
WORKDIR /var/www/html

# Copy existing application directory contents
COPY . /var/www/html

# Copy existing application directory permissions
COPY --chown=www-data:www-data . /var/www/html

# Expose port 9000 and start PHP-FPM server
EXPOSE 9000
CMD ["php-fpm"]
