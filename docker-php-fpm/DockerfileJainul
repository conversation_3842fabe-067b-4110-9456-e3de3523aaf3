# Use the official PHP image as a parent image
FROM php:8.1-fpm

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies and build tools
RUN apt-get update && apt-get install -y \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    libzip-dev \
    libonig-dev \
    libxml2-dev \
    libxslt-dev \
    libicu-dev \
    libgmp-dev \
    default-mysql-client \
    unzip \
    git \
    curl \
    cron \
    sudo \
    gnupg2 \
    ca-certificates \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd intl zip bcmath pdo_mysql opcache soap xsl gmp sockets

# Install Node.js and npm from NodeSource
RUN apt-get update && \
    apt-get install -y nodejs npm

# Install Composer globally (version 2)
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# Install Xdebug
# RUN pecl install xdebug-3.2.2 && docker-php-ext-enable xdebug

# Set working directory
WORKDIR /var/www/html

# Copy application files and set permissions
COPY --chown=www-data:www-data . /var/www/html

# Copy custom PHP config
COPY php.ini /usr/local/etc/php/

# Expose PHP-FPM port
EXPOSE 9000

# Start PHP-FPM
CMD ["php-fpm"]
