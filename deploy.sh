#!/bin/bash

composer install
php bin/magento main:en
php bin/magento se:up &&
php bin/magento se:di:co &&
php bin/magento se:st:de -f en_US sv_SE da_DK fi_FI de_DE nb_NO fr_FR it_IT --area frontend --theme MadHat/technology --jobs=16 &&
php bin/magento se:st:de -f en_US sv_SE da_DK fi_FI de_DE nb_NO fr_FR it_IT --area frontend --theme Magento/luma --jobs=16 &&
php bin/magento se:st:de -f en_US sv_SE --area adminhtml --jobs=16 &&
php bin/magento main:di
php bin/magento ind:res
php bin/magento c:st
redis-cli FLUSHALL
php bin/magento c:f
# shellcheck disable=SC2164
npm --prefix app/design/frontend/MadHat/technology/web/tailwind run watch
