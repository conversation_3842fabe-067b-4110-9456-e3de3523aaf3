<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="module9_test_main_table" resource="default" engine="innodb" comment="Main Test Table for Module9">
        <column xsi:type="int" name="module9_email_contact_id" padding="10" unsigned="true" nullable="false"
                identity="true" comment="Entity ID"/>
        <column xsi:type="smallint" name="module9_is_guest" padding="5" unsigned="true" nullable="true"
                identity="false" comment="Is Guest"/>
        <column xsi:type="int" name="module9_guest_id" padding="10" unsigned="true" nullable="true" identity="false"
                comment="Guest ID"/>
        <column xsi:type="date" name="module9_created_at" comment="Created At"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="module9_email_contact_id"/>
        </constraint>
        <constraint xsi:type="unique" referenceId="MODULE9_INSTALL_UNIQUE_INDEX_1">
            <column name="module9_email_contact_id"/>
            <column name="module9_guest_id"/>
        </constraint>
        <column xsi:type="int" name="module9_update_column" padding="11" unsigned="false" nullable="false"
                identity="false" comment="Module_9 Update Column"/>
        <constraint xsi:type="foreign" referenceId="MODULE9_UPDATE_FK_MODULE9_IS_GUEST" table="module9_test_main_table"
                    column="module9_is_guest" referenceTable="module8_test_main_table"
                    referenceColumn="module8_is_guest" onDelete="CASCADE"/>
    </table>
    <table name="module8_test_main_table" resource="default">
        <column xsi:type="int" name="module9_update_column" padding="11" unsigned="false" nullable="false"
                identity="false" comment="Module_9 Update Column"/>
    </table>
    <table name="module8_test_update_table" resource="default">
        <column name="module8_column_for_remove" disabled="true"/>
        <constraint xsi:type="foreign" referenceId="MODULE8_UPDATE_FK_TEMP" disabled="true"/>
        <index referenceId="MODULE9_UPDATE_MODULE8_GUEST_BROWSER_ID" indexType="btree">
            <column name="module8_guest_browser_id"/>
        </index>
        <index referenceId="MODULE8_UPDATE_UNIQUE_INDEX_TEMP" disabled="true"/>
    </table>
    <table name="module8_test_second_table" resource="default">
        <constraint xsi:type="foreign" referenceId="MODULE8_INSTALL_FK_ADDRESS_TEST_MAIN_TABLE_CONTACT_ID"
                    disabled="true"/>
        <index referenceId="MODULE8_INSTALL_SECOND_TABLE_INDEX_3_TEMP" disabled="true"/>
    </table>
    <table name="module8_test_temp_table" disabled="true" resource="default"/>
    <table name="module9_test_update_replica_table" resource="default" engine="innodb"
           comment="Module9 Test Update Replica Table">
        <column xsi:type="int" name="module8_entity_id" padding="10" unsigned="true" nullable="false" identity="true"
                comment="Module8 Entity Id"/>
        <column xsi:type="int" name="module8_contact_id" padding="11" unsigned="false" nullable="true"
                identity="false" comment="Module8 Contact Id"/>
        <column xsi:type="varchar" name="module8_address" nullable="false" length="15" comment="Module8 Address"/>
        <column xsi:type="smallint" name="module8_counter_with_multiline_comment" padding="5" unsigned="true"
                nullable="true" identity="false" default="0" comment="Module8 Counter With Multiline Comment"/>
        <column xsi:type="varchar" name="module8_second_address" nullable="true" length="15"
                comment="Module8 Second Address"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="module8_entity_id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="FK_F205D8789B56A8E75BBFC0C68C041E98"
                    table="module9_test_update_replica_table" column="module8_address"
                    referenceTable="module8_test_main_table" referenceColumn="module8_content" onDelete="NO ACTION"/>
        <constraint xsi:type="foreign" referenceId="FK_C7075560727757663A51EC925F4032C9"
                    table="module9_test_update_replica_table" column="module8_address"
                    referenceTable="module8_test_main_table" referenceColumn="module8_contact_id" onDelete="NO ACTION"/>
        <constraint xsi:type="foreign" referenceId="FK_8914AF398964FAFB4ED2E382866ABBF4"
                    table="module9_test_update_replica_table" column="module8_entity_id"
                    referenceTable="module8_test_main_table" referenceColumn="module8_email_contact_id"
                    onDelete="NO ACTION"/>
        <index referenceId="MODULE9_TEST_UPDATE_REPLICA_TABLE_MODULE8_ENTITY_ID" indexType="btree">
            <column name="module8_entity_id"/>
        </index>
        <index referenceId="MODULE9_TEST_UPDATE_REPLICA_TABLE_MODULE8_ADDRESS" indexType="btree">
            <column name="module8_address"/>
        </index>
        <index referenceId="MODULE9_TEST_UPDATE_REPLICA_TABLE_MODULE8_SECOND_ADDRESS" indexType="btree">
            <column name="module8_second_address"/>
        </index>
    </table>
</schema>
