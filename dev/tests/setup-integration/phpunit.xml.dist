<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd"
         colors="true"
         columns="max"
         beStrictAboutTestsThatDoNotTestAnything="false"
         bootstrap="./framework/bootstrap.php"
         stderr="true">
    <!-- Code coverage filters -->
    <coverage>
        <include>
            <directory suffix=".php">../../../app/code/Magento</directory>
            <directory suffix=".php">../../../lib/internal/Magento</directory>
        </include>
    </coverage>
    <!-- Test suites definition -->
    <testsuites>
        <testsuite name="Magento Setup/Upgrade Tests">
            <directory>testsuite</directory>
        </testsuite>
    </testsuites>
    <!-- PHP INI settings and constants definition -->
    <php>
        <includePath>.</includePath>
        <includePath>testsuite</includePath>
        <ini name="date.timezone" value="America/Los_Angeles"/>
        <ini name="xdebug.max_nesting_level" value="200"/>
        <const name="TESTS_INSTALL_CONFIG_FILE" value="{{local_config_file}}"/>
        <const name="TESTS_GLOBAL_CONFIG_FILE" value="etc/config-global.php"/>
        <const name="TESTS_GLOBAL_CONFIG_DIR" value="../../../app/etc"/>
        <const name="TESTS_CLEANUP" value="{{tests_cleanup}}"/>
        <const name="TESTS_MAGENTO_MODE" value="{{app_mode}}"/>
        <const name="TESTS_ERROR_LOG_LISTENER_LEVEL" value="1"/>
    </php>
    <!-- Test listeners -->
    <listeners>
        <listener class="Magento\TestFramework\Event\PhpUnit"/>
        <listener class="Magento\TestFramework\ErrorLog\Listener"/>
    </listeners>
    <extensions>
        <extension class="Qameta\Allure\PHPUnit\AllureExtension">
            <!-- Optional arguments block; omit it if you want to use default values -->
            <arguments>
                <!-- Path to config file (default is config/allure.config.php) -->
                <string>allure/allure.config.php</string>
            </arguments>
        </extension>
    </extensions>
</phpunit>
